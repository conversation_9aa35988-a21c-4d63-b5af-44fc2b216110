#!/usr/bin/env python3
"""
表格标注优化器 - 使用改进的透视感知算法优化表格标注

主要功能:
1. 保持透视变换特性，支持四边形单元格
2. 自适应阈值计算，基于图像分辨率和表格尺寸
3. 保留原始quality和type属性不变
4. 支持批量处理和并行处理
5. 可配置输入输出路径和算法参数

基于透视感知算法，确保角点对齐的同时保持表格的自然透视特征。

作者: AI Assistant
版本: 3.0
更新日期: 2025-01-08
"""

import os
import json
import time
import math
import numpy as np
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
import argparse
from typing import List, Dict, Tuple, Optional, Any
from PIL import Image


# ==================== 配置区域 ====================
# 在这里修改处理参数
CONFIG = {
    # 输入输出路径配置
    'input_dir': "F:\workspace\datasets\Relabel_TabRecSet\chinese\curved_tables",           # 输入文件夹路径
    'output_dir': "F:\workspace\datasets\Relabel_TabRecSet\chinese\curved_tables_1", # 输出文件夹路径

    # 文件匹配模式
    'annotation_pattern': '*_table_annotation.json',  # 标注文件匹配模式
    'image_extensions': ['.jpg', '.jpeg', '.png', '.bmp', '.tiff'],  # 支持的图片格式

    # 算法参数（保守模式）
    'tolerance': 1.5,              # 基础容差阈值（像素）- 更保守
    'preserve_perspective': True,   # 是否保持透视变换
    'adaptive_threshold': False,    # 关闭自适应阈值，使用固定阈值
    'quality_aware': False,        # 关闭质量感知优化，避免过度调整
    'angle_correction': False,     # 关闭角度校正，避免过度修改
    'conservative_mode': True,     # 启用保守模式

    # 处理参数
    'max_workers': 1,              # 并行处理线程数
    'copy_images': True,           # 是否复制图片文件到输出目录

    # 属性保护配置
    'preserve_attributes': ['quality', 'type'],  # 需要保护的属性列表
}
# ================================================


class PerspectiveAwareOptimizer:
    """透视感知表格标注优化器类"""

    def __init__(self, tolerance: float = 3.0, preserve_perspective: bool = True,
                 adaptive_threshold: bool = True, quality_aware: bool = False,
                 angle_correction: bool = False, conservative_mode: bool = True):
        """
        初始化优化器

        Args:
            tolerance: 基础容差阈值（像素）
            preserve_perspective: 是否保持透视变换
            adaptive_threshold: 是否使用自适应阈值
            quality_aware: 是否启用质量感知优化
            angle_correction: 是否启用角度校正
            conservative_mode: 是否启用保守模式（默认启用，减少过度修改）
        """
        self.base_tolerance = tolerance
        self.preserve_perspective = preserve_perspective
        self.adaptive_threshold = adaptive_threshold
        self.quality_aware = quality_aware
        self.angle_correction = angle_correction
        self.conservative_mode = conservative_mode
        self.tolerance = tolerance
        self.image_info = None
        self.cells = []
        self.table_ind = ""
        self.image_path = ""
        self.grid_structure = {}

        # 保守模式参数
        if self.conservative_mode:
            self.overlap_threshold = 1.0  # 更严格的重叠阈值
            self.quality_tolerance_factor = 1.1  # 更保守的容差因子
            self.regularity_threshold = 0.98  # 更高的规整度要求
            self.angle_tolerance = 1.0  # 更严格的角度容差
            self.max_adjustment_ratio = 0.1  # 最大调整比例（10%）
        else:
            # 原有参数
            self.overlap_threshold = 0.5
            self.quality_tolerance_factor = 1.5
            self.regularity_threshold = 0.95
            self.angle_tolerance = 2.0
            self.max_adjustment_ratio = 0.3

        self.detected_rotation = 0.0  # 检测到的图片旋转角度

    def is_adjustment_reasonable(self, original_point: List[float],
                               new_point: List[float]) -> bool:
        """
        检查调整是否合理（保守模式）

        Args:
            original_point: 原始点坐标
            new_point: 新点坐标

        Returns:
            是否合理
        """
        if not self.conservative_mode:
            return True

        # 计算调整距离
        distance = math.sqrt((new_point[0] - original_point[0])**2 +
                           (new_point[1] - original_point[1])**2)

        # 计算相对于图片尺寸的调整比例
        if self.image_info:
            max_dimension = max(self.image_info['width'], self.image_info['height'])
            adjustment_ratio = distance / max_dimension

            # 如果调整比例超过阈值，认为不合理
            if adjustment_ratio > self.max_adjustment_ratio:
                return False

        # 如果调整距离超过容差的3倍，认为不合理
        if distance > self.tolerance * 3:
            return False

        return True

    def conservative_update_point(self, cell_idx: int, point_name: str,
                                new_coords: List[float]) -> bool:
        """
        保守地更新点坐标

        Args:
            cell_idx: 单元格索引
            point_name: 点名称
            new_coords: 新坐标

        Returns:
            是否更新成功
        """
        original_coords = self.cells[cell_idx]['bbox'][point_name]

        # 检查调整是否合理
        if self.is_adjustment_reasonable(original_coords, new_coords):
            self.cells[cell_idx]['bbox'][point_name] = new_coords
            return True
        else:
            # 如果调整过大，只进行部分调整
            if self.conservative_mode:
                # 只调整原始距离的50%
                dx = (new_coords[0] - original_coords[0]) * 0.5
                dy = (new_coords[1] - original_coords[1]) * 0.5
                conservative_coords = [original_coords[0] + dx, original_coords[1] + dy]
                self.cells[cell_idx]['bbox'][point_name] = conservative_coords
                return True
            return False

    def get_image_info(self, image_path: str) -> Optional[Dict[str, int]]:
        """
        获取图片信息

        Args:
            image_path: 图片文件路径

        Returns:
            包含图片宽度和高度的字典，失败时返回None
        """
        try:
            if image_path and Path(image_path).exists():
                with Image.open(image_path) as img:
                    return {'width': img.size[0], 'height': img.size[1]}
        except Exception as e:
            print(f"无法读取图片 {image_path}: {e}")
        return None

    def calculate_table_bounds(self, cells: List[Dict]) -> Tuple[float, float]:
        """
        计算表格边界尺寸

        Args:
            cells: 单元格列表

        Returns:
            表格宽度和高度的元组
        """
        if not cells:
            return 0.0, 0.0

        all_x = []
        all_y = []

        for cell in cells:
            bbox = cell['bbox']
            for point_name in ['p1', 'p2', 'p3', 'p4']:
                point = bbox[point_name]
                all_x.append(point[0])
                all_y.append(point[1])

        width = max(all_x) - min(all_x)
        height = max(all_y) - min(all_y)

        return width, height

    def calculate_adaptive_threshold(self, img_width: int, img_height: int,
                                   table_width: float, table_height: float) -> float:
        """
        基于图片分辨率和表格尺寸计算改进的自适应阈值

        Args:
            img_width: 图片宽度
            img_height: 图片高度
            table_width: 表格宽度
            table_height: 表格高度

        Returns:
            计算得到的自适应阈值
        """
        if not self.adaptive_threshold:
            return self.base_tolerance

        # 1. 分辨率因子 - 改进的计算方式
        total_pixels = img_width * img_height
        resolution_factor = math.sqrt(total_pixels / (1920 * 1080))  # 以1080p为基准
        resolution_factor = max(0.3, min(3.0, resolution_factor))

        # 2. 表格尺寸因子 - 更精细的分级
        if table_width > 0 and table_height > 0:
            table_area = table_width * table_height
            image_area = img_width * img_height
            area_ratio = table_area / image_area

            # 根据表格占比调整阈值
            if area_ratio > 0.9:        # 表格几乎占满整个图片
                size_factor = 0.6
            elif area_ratio > 0.7:      # 表格占主导地位
                size_factor = 0.8
            elif area_ratio > 0.5:      # 表格占一半以上
                size_factor = 1.0
            elif area_ratio > 0.3:      # 表格占中等比例
                size_factor = 1.2
            else:                       # 表格占比较小
                size_factor = 1.5
        else:
            size_factor = 1.0

        # 3. 单元格密度因子
        cell_count = len(self.cells)
        if table_width > 0 and table_height > 0 and cell_count > 0:
            avg_cell_area = (table_width * table_height) / cell_count
            avg_cell_size = math.sqrt(avg_cell_area)

            # 单元格越小，需要更精确的阈值
            if avg_cell_size < 30:
                density_factor = 0.7
            elif avg_cell_size < 50:
                density_factor = 0.9
            elif avg_cell_size < 100:
                density_factor = 1.0
            else:
                density_factor = 1.3
        else:
            density_factor = 1.0

        # 4. 质量感知因子（基于97个合格样本分析）
        if self.quality_aware:
            # 合格样本平均对齐误差为9.01px，需要更宽松的阈值
            quality_factor = self.quality_tolerance_factor
        else:
            quality_factor = 1.0

        # 5. 综合计算 - 调整权重
        combined_factor = (
            0.3 * resolution_factor +
            0.3 * size_factor +
            0.2 * density_factor +
            0.2 * quality_factor
        )
        adaptive_threshold = self.base_tolerance * combined_factor

        # 限制阈值范围 [0.5, 20.0] - 质量感知模式允许更大阈值
        max_threshold = 20.0 if self.quality_aware else 15.0
        adaptive_threshold = max(0.5, min(max_threshold, adaptive_threshold))

        return adaptive_threshold

    def load_annotation(self, json_path: str, image_path: Optional[str] = None):
        """
        加载表格标注文件

        Args:
            json_path: 标注文件路径
            image_path: 对应的图片文件路径（用于自适应阈值计算）
        """
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 保存原始数据以便保护所有属性
        self.original_data = data.copy()

        self.cells = data['cells']
        self.table_ind = data.get('table_ind', '')
        self.image_path = data.get('image_path', '')

        # 获取图片信息用于自适应阈值计算
        if image_path:
            self.image_info = self.get_image_info(image_path)

            # 检测图片旋转角度
            if self.angle_correction:
                self.detected_rotation = self.detect_image_rotation(image_path)

            # 计算表格边界用于自适应阈值
            if self.image_info:
                table_width, table_height = self.calculate_table_bounds(self.cells)
                self.tolerance = self.calculate_adaptive_threshold(
                    self.image_info['width'],
                    self.image_info['height'],
                    table_width,
                    table_height
                )
                print(f"自适应阈值: {self.tolerance:.2f} (图片: {self.image_info['width']}x{self.image_info['height']}, 表格: {table_width:.1f}x{table_height:.1f})")
            else:
                print(f"无法获取图片信息，使用基础阈值: {self.base_tolerance}")
                self.tolerance = self.base_tolerance

        print(f"加载了 {len(self.cells)} 个单元格")

    def build_grid_structure(self):
        """构建表格的逻辑网格结构"""
        self.grid_structure = {}

        for cell in self.cells:
            lloc = cell['lloc']
            for r in range(lloc['start_row'], lloc['end_row'] + 1):
                for c in range(lloc['start_col'], lloc['end_col'] + 1):
                    if (r, c) not in self.grid_structure:
                        self.grid_structure[(r, c)] = []
                    self.grid_structure[(r, c)].append(cell)

    def get_shared_boundary_points(self, direction: str) -> List[Tuple[List[float], List[int]]]:
        """
        获取共享边界上的角点

        Args:
            direction: 'horizontal' 或 'vertical'

        Returns:
            (点坐标, 相关单元格索引列表) 的列表
        """
        boundary_points = []

        if direction == 'horizontal':
            # 处理水平边界（行之间的边界）
            rows = sorted(set(r for r, c in self.grid_structure.keys()))
            cols = sorted(set(c for r, c in self.grid_structure.keys()))

            for row_idx in range(len(rows) - 1):
                current_row = rows[row_idx]
                next_row = rows[row_idx + 1]

                for col in cols:
                    current_cells = self.grid_structure.get((current_row, col), [])
                    next_cells = self.grid_structure.get((next_row, col), [])

                    if current_cells and next_cells:
                        for curr_cell in current_cells:
                            for next_cell in next_cells:
                                if (curr_cell['lloc']['end_row'] == current_row and
                                    next_cell['lloc']['start_row'] == next_row):
                                    # 收集共享边界上的点
                                    curr_idx = self.cells.index(curr_cell)
                                    next_idx = self.cells.index(next_cell)

                                    # 当前单元格的下边界点
                                    boundary_points.append((curr_cell['bbox']['p3'], [curr_idx]))
                                    boundary_points.append((curr_cell['bbox']['p4'], [curr_idx]))

                                    # 下一个单元格的上边界点
                                    boundary_points.append((next_cell['bbox']['p1'], [next_idx]))
                                    boundary_points.append((next_cell['bbox']['p2'], [next_idx]))

        elif direction == 'vertical':
            # 处理垂直边界（列之间的边界）
            rows = sorted(set(r for r, c in self.grid_structure.keys()))
            cols = sorted(set(c for r, c in self.grid_structure.keys()))

            for col_idx in range(len(cols) - 1):
                current_col = cols[col_idx]
                next_col = cols[col_idx + 1]

                for row in rows:
                    current_cells = self.grid_structure.get((row, current_col), [])
                    next_cells = self.grid_structure.get((row, next_col), [])

                    if current_cells and next_cells:
                        for curr_cell in current_cells:
                            for next_cell in next_cells:
                                if (curr_cell['lloc']['end_col'] == current_col and
                                    next_cell['lloc']['start_col'] == next_col):
                                    # 收集共享边界上的点
                                    curr_idx = self.cells.index(curr_cell)
                                    next_idx = self.cells.index(next_cell)

                                    # 当前单元格的右边界点
                                    boundary_points.append((curr_cell['bbox']['p2'], [curr_idx]))
                                    boundary_points.append((curr_cell['bbox']['p3'], [curr_idx]))

                                    # 下一个单元格的左边界点
                                    boundary_points.append((next_cell['bbox']['p1'], [next_idx]))
                                    boundary_points.append((next_cell['bbox']['p4'], [next_idx]))

        return boundary_points

    def align_boundary_points(self, boundary_points: List[Tuple[List[float], List[int]]],
                            coordinate_index: int):
        """
        改进的边界点对齐算法

        Args:
            boundary_points: 边界点列表
            coordinate_index: 坐标索引（0=x, 1=y）
        """
        if not boundary_points:
            return

        # 使用改进的聚类算法
        clusters = self._improved_clustering(boundary_points, coordinate_index)

        # 对每个聚类进行智能对齐
        aligned_count = 0
        for cluster in clusters:
            if len(cluster) < 2:
                continue

            # 使用加权平均而不是简单平均
            aligned_coord = self._calculate_weighted_average(cluster, coordinate_index)

            # 更新所有相关单元格的角点（保守模式）
            for point, cell_indices, _ in cluster:
                for cell_idx in cell_indices:
                    cell = self.cells[cell_idx]
                    bbox = cell['bbox']

                    # 找到对应的角点并更新
                    for point_name in ['p1', 'p2', 'p3', 'p4']:
                        if (abs(bbox[point_name][0] - point[0]) < 0.1 and
                            abs(bbox[point_name][1] - point[1]) < 0.1):

                            # 保守更新：创建新坐标
                            new_coords = bbox[point_name].copy()
                            new_coords[coordinate_index] = aligned_coord

                            # 使用保守更新方法
                            self.conservative_update_point(cell_idx, point_name, new_coords)
                            aligned_count += 1

        if aligned_count > 0:
            print(f"  对齐了 {aligned_count} 个边界点")

    def _improved_clustering(self, boundary_points: List[Tuple[List[float], List[int]]],
                           coordinate_index: int) -> List[List[Tuple]]:
        """
        改进的聚类算法，使用动态阈值

        Args:
            boundary_points: 边界点列表
            coordinate_index: 坐标索引

        Returns:
            聚类结果
        """
        if not boundary_points:
            return []

        # 按坐标值排序
        sorted_points = sorted(enumerate(boundary_points),
                             key=lambda x: x[1][0][coordinate_index])

        clusters = []
        current_cluster = []

        for i, (orig_idx, (point, cells)) in enumerate(sorted_points):
            if not current_cluster:
                current_cluster = [(point, cells, orig_idx)]
            else:
                # 计算与当前聚类的距离
                last_point = current_cluster[-1][0]
                distance = abs(point[coordinate_index] - last_point[coordinate_index])

                # 动态阈值：考虑聚类内的方差
                dynamic_threshold = self._calculate_dynamic_threshold(current_cluster, coordinate_index)

                if distance <= dynamic_threshold:
                    current_cluster.append((point, cells, orig_idx))
                else:
                    # 开始新聚类
                    if len(current_cluster) > 1:
                        clusters.append(current_cluster)
                    current_cluster = [(point, cells, orig_idx)]

        # 添加最后一个聚类
        if len(current_cluster) > 1:
            clusters.append(current_cluster)

        return clusters

    def _calculate_dynamic_threshold(self, cluster: List[Tuple], coordinate_index: int) -> float:
        """
        计算动态阈值

        Args:
            cluster: 当前聚类
            coordinate_index: 坐标索引

        Returns:
            动态阈值
        """
        if len(cluster) < 2:
            return self.tolerance

        coords = [point[coordinate_index] for point, _, _ in cluster]
        variance = sum((x - sum(coords)/len(coords))**2 for x in coords) / len(coords)
        std_dev = math.sqrt(variance)

        # 基于标准差调整阈值
        return max(self.tolerance * 0.5, min(self.tolerance * 2.0, self.tolerance + std_dev))

    def _calculate_weighted_average(self, cluster: List[Tuple], coordinate_index: int) -> float:
        """
        计算加权平均坐标

        Args:
            cluster: 聚类点
            coordinate_index: 坐标索引

        Returns:
            加权平均坐标
        """
        total_weight = 0
        weighted_sum = 0

        for point, cell_indices, _ in cluster:
            # 权重基于相关单元格数量
            weight = len(cell_indices)
            weighted_sum += point[coordinate_index] * weight
            total_weight += weight

        return weighted_sum / total_weight if total_weight > 0 else sum(point[coordinate_index] for point, _, _ in cluster) / len(cluster)

    def align_row_boundaries(self):
        """对齐行边界（水平对齐）"""
        boundary_points = self.get_shared_boundary_points('horizontal')
        self.align_boundary_points(boundary_points, 1)  # 对齐Y坐标

    def align_column_boundaries(self):
        """对齐列边界（垂直对齐）"""
        boundary_points = self.get_shared_boundary_points('vertical')
        self.align_boundary_points(boundary_points, 0)  # 对齐X坐标

    def fine_tune_nearby_points(self):
        """改进的微调相近角点算法"""
        if self.quality_aware:
            # 使用质量感知微调
            self.quality_aware_fine_tuning()
        else:
            # 使用传统多级微调
            all_points = []

            # 收集所有角点
            for cell_idx, cell in enumerate(self.cells):
                bbox = cell['bbox']
                for point_name in ['p1', 'p2', 'p3', 'p4']:
                    point = bbox[point_name]
                    all_points.append({
                        'coords': point,
                        'cell_idx': cell_idx,
                        'point_name': point_name
                    })

            # 使用多级微调策略
            self._multi_level_fine_tuning(all_points)

    def _multi_level_fine_tuning(self, all_points: List[Dict]):
        """
        多级微调策略

        Args:
            all_points: 所有角点列表
        """
        # 第一级：精确微调（很小的阈值）
        micro_threshold = self.tolerance * 0.3
        aligned_count_1 = self._fine_tune_with_threshold(all_points, micro_threshold, "精确")

        # 第二级：中等微调
        medium_threshold = self.tolerance * 0.6
        aligned_count_2 = self._fine_tune_with_threshold(all_points, medium_threshold, "中等")

        total_aligned = aligned_count_1 + aligned_count_2
        if total_aligned > 0:
            print(f"微调了 {total_aligned} 个相近角点 (精确: {aligned_count_1}, 中等: {aligned_count_2})")

    def _fine_tune_with_threshold(self, all_points: List[Dict], threshold: float, level: str) -> int:
        """
        使用指定阈值进行微调

        Args:
            all_points: 所有角点列表
            threshold: 微调阈值
            level: 微调级别名称

        Returns:
            对齐的点数
        """
        aligned_count = 0
        used_indices = set()

        for i, point1 in enumerate(all_points):
            if i in used_indices:
                continue

            cluster = [point1]
            used_indices.add(i)

            for j, point2 in enumerate(all_points):
                if j in used_indices:
                    continue

                # 计算欧几里得距离
                dist = math.sqrt(
                    (point1['coords'][0] - point2['coords'][0])**2 +
                    (point1['coords'][1] - point2['coords'][1])**2
                )

                if dist <= threshold:
                    cluster.append(point2)
                    used_indices.add(j)

            # 如果聚类包含多个点，进行智能对齐
            if len(cluster) > 1:
                # 使用加权重心计算
                center_x, center_y = self._calculate_cluster_center(cluster)

                # 保守地更新所有点到重心位置
                for point in cluster:
                    new_coords = [center_x, center_y]
                    if self.conservative_update_point(point['cell_idx'], point['point_name'], new_coords):
                        aligned_count += 1

        return aligned_count

    def _calculate_cluster_center(self, cluster: List[Dict]) -> Tuple[float, float]:
        """
        计算聚类的加权重心

        Args:
            cluster: 角点聚类

        Returns:
            重心坐标 (x, y)
        """
        # 简单平均（可以根据需要添加权重）
        center_x = sum(p['coords'][0] for p in cluster) / len(cluster)
        center_y = sum(p['coords'][1] for p in cluster) / len(cluster)

        return center_x, center_y

    def detect_overlapping_points(self) -> List[List[Dict]]:
        """
        检测重叠或极近的角点（基于质量样本分析）

        Returns:
            重叠角点组列表
        """
        if not self.quality_aware:
            return []

        all_points = []

        # 收集所有角点
        for cell_idx, cell in enumerate(self.cells):
            bbox = cell['bbox']
            for point_name in ['p1', 'p2', 'p3', 'p4']:
                point = bbox[point_name]
                all_points.append({
                    'coords': point,
                    'cell_idx': cell_idx,
                    'point_name': point_name
                })

        # 检测重叠点组
        overlap_groups = []
        used_indices = set()

        for i, point1 in enumerate(all_points):
            if i in used_indices:
                continue

            group = [point1]
            used_indices.add(i)

            for j, point2 in enumerate(all_points):
                if j in used_indices:
                    continue

                # 计算距离
                dist = math.sqrt(
                    (point1['coords'][0] - point2['coords'][0])**2 +
                    (point1['coords'][1] - point2['coords'][1])**2
                )

                if dist <= self.overlap_threshold:
                    group.append(point2)
                    used_indices.add(j)

            if len(group) > 1:
                overlap_groups.append(group)

        return overlap_groups

    def resolve_overlapping_points(self):
        """
        解决重叠角点问题（基于质量样本分析）
        """
        if not self.quality_aware:
            return

        overlap_groups = self.detect_overlapping_points()

        if not overlap_groups:
            return

        print(f"  发现 {len(overlap_groups)} 组重叠角点")

        resolved_count = 0
        for group in overlap_groups:
            if len(group) < 2:
                continue

            # 计算组内角点的加权中心
            center_x = sum(p['coords'][0] for p in group) / len(group)
            center_y = sum(p['coords'][1] for p in group) / len(group)

            # 更新所有重叠点到中心位置
            for point in group:
                cell = self.cells[point['cell_idx']]
                cell['bbox'][point['point_name']][0] = center_x
                cell['bbox'][point['point_name']][1] = center_y
                resolved_count += 1

        print(f"  解决了 {resolved_count} 个重叠角点")

    def calculate_cell_regularity(self, cell: Dict) -> float:
        """
        计算单元格的规整度（基于质量样本分析）

        Args:
            cell: 单元格数据

        Returns:
            规整度分数 (0-1)
        """
        bbox = cell.get('bbox', {})
        if not all(p in bbox for p in ['p1', 'p2', 'p3', 'p4']):
            return 0.0

        # 获取四个角点
        p1, p2, p3, p4 = [bbox[p] for p in ['p1', 'p2', 'p3', 'p4']]

        # 计算四条边的长度
        side1 = math.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)
        side2 = math.sqrt((p3[0] - p2[0])**2 + (p3[1] - p2[1])**2)
        side3 = math.sqrt((p4[0] - p3[0])**2 + (p4[1] - p3[1])**2)
        side4 = math.sqrt((p1[0] - p4[0])**2 + (p1[1] - p4[1])**2)

        # 对边长度比
        if side1 > 0 and side3 > 0:
            ratio1 = min(side1, side3) / max(side1, side3)
        else:
            ratio1 = 0

        if side2 > 0 and side4 > 0:
            ratio2 = min(side2, side4) / max(side2, side4)
        else:
            ratio2 = 0

        return (ratio1 + ratio2) / 2

    def quality_aware_fine_tuning(self):
        """
        基于质量样本特征的精细调整
        """
        print("执行质量感知微调...")

        # 1. 首先解决重叠角点
        self.resolve_overlapping_points()

        # 2. 基于规整度进行分组调整
        high_regularity_cells = []
        low_regularity_cells = []

        for cell in self.cells:
            regularity = self.calculate_cell_regularity(cell)
            if regularity >= self.regularity_threshold:
                high_regularity_cells.append(cell)
            else:
                low_regularity_cells.append(cell)

        print(f"  高规整度单元格: {len(high_regularity_cells)}")
        print(f"  低规整度单元格: {len(low_regularity_cells)}")

        # 3. 对高规整度单元格使用更精确的对齐
        if high_regularity_cells:
            self._fine_tune_regular_cells(high_regularity_cells)

        # 4. 对低规整度单元格使用更宽松的对齐
        if low_regularity_cells:
            self._fine_tune_irregular_cells(low_regularity_cells)

    def _fine_tune_regular_cells(self, cells: List[Dict]):
        """对规整单元格进行精确微调"""
        # 使用更小的阈值
        precise_threshold = self.tolerance * 0.3

        all_points = []
        for cell in cells:
            cell_idx = self.cells.index(cell)
            bbox = cell['bbox']
            for point_name in ['p1', 'p2', 'p3', 'p4']:
                point = bbox[point_name]
                all_points.append({
                    'coords': point,
                    'cell_idx': cell_idx,
                    'point_name': point_name
                })

        aligned_count = self._fine_tune_with_threshold(all_points, precise_threshold, "精确")
        print(f"  精确微调了 {aligned_count} 个规整单元格角点")

    def _fine_tune_irregular_cells(self, cells: List[Dict]):
        """对不规整单元格进行宽松微调"""
        # 使用更大的阈值
        loose_threshold = self.tolerance * 0.8

        all_points = []
        for cell in cells:
            cell_idx = self.cells.index(cell)
            bbox = cell['bbox']
            for point_name in ['p1', 'p2', 'p3', 'p4']:
                point = bbox[point_name]
                all_points.append({
                    'coords': point,
                    'cell_idx': cell_idx,
                    'point_name': point_name
                })

        aligned_count = self._fine_tune_with_threshold(all_points, loose_threshold, "宽松")
        print(f"  宽松微调了 {aligned_count} 个不规整单元格角点")

    def detect_image_rotation(self, image_path: str) -> float:
        """
        检测图片旋转角度

        Args:
            image_path: 图片路径

        Returns:
            检测到的旋转角度（度）
        """
        if not self.angle_correction:
            return 0.0

        try:
            import cv2

            # 读取图片
            image = cv2.imread(image_path)
            if image is None:
                print(f"无法读取图片: {image_path}")
                return 0.0

            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # 边缘检测
            edges = cv2.Canny(gray, 50, 150, apertureSize=3)

            # 霍夫线变换检测直线
            lines = cv2.HoughLines(edges, 1, math.pi/180, threshold=100)

            if lines is None:
                print("未检测到足够的直线")
                return 0.0

            # 分析线条角度
            angles = []
            for line in lines:
                rho, theta = line[0]
                angle = theta * 180 / math.pi

                # 将角度标准化到0-90度范围
                if angle > 90:
                    angle = angle - 180
                elif angle < -90:
                    angle = angle + 180

                angles.append(angle)

            # 找到主要的水平方向
            horizontal_angles = [a for a in angles if abs(a) < 25]

            if horizontal_angles:
                avg_horizontal = sum(horizontal_angles) / len(horizontal_angles)
                rotation_angle = -avg_horizontal  # 负号表示需要反向旋转来校正
                print(f"检测到图片旋转角度: {rotation_angle:.2f}度")
                return rotation_angle

            return 0.0

        except ImportError:
            print("OpenCV未安装，跳过角度检测")
            return 0.0
        except Exception as e:
            print(f"角度检测失败: {e}")
            return 0.0

    def calculate_line_angle(self, p1: List[float], p2: List[float]) -> float:
        """
        计算两点间直线的角度

        Args:
            p1: 起点坐标
            p2: 终点坐标

        Returns:
            角度（度）
        """
        dx = p2[0] - p1[0]
        dy = p2[1] - p1[1]

        if abs(dx) < 1e-6:  # 垂直线
            return 90.0

        angle = math.atan2(dy, dx) * 180 / math.pi
        return angle

    def find_shared_points(self) -> Dict[str, List[Dict]]:
        """
        改进的共享角点检测，使用更精确的聚类方法

        Returns:
            共享点字典，键为坐标字符串，值为使用该点的单元格和角点信息
        """
        all_points = []

        # 收集所有角点
        for cell_idx, cell in enumerate(self.cells):
            bbox = cell['bbox']
            for point_name in ['p1', 'p2', 'p3', 'p4']:
                if point_name in bbox:
                    coords = bbox[point_name]
                    all_points.append({
                        'cell_idx': cell_idx,
                        'point_name': point_name,
                        'coords': coords,
                        'x': coords[0],
                        'y': coords[1]
                    })

        # 使用距离聚类找到真正的共享点
        shared_groups = self._cluster_nearby_points(all_points)

        # 转换为字典格式
        shared_points = {}
        for group_idx, group in enumerate(shared_groups):
            if len(group) > 1:  # 只保留真正共享的点
                # 计算组的中心坐标作为键
                center_x = sum(p['x'] for p in group) / len(group)
                center_y = sum(p['y'] for p in group) / len(group)
                coord_key = f"group_{group_idx}_{center_x:.1f},{center_y:.1f}"

                shared_points[coord_key] = group

        return shared_points

    def _cluster_nearby_points(self, all_points: List[Dict]) -> List[List[Dict]]:
        """
        使用距离聚类算法找到相近的角点

        Args:
            all_points: 所有角点列表

        Returns:
            聚类结果
        """
        if not all_points:
            return []

        # 使用更小的阈值进行聚类
        cluster_threshold = min(self.tolerance, 2.0)  # 最大2像素

        clusters = []
        used_indices = set()

        for i, point1 in enumerate(all_points):
            if i in used_indices:
                continue

            cluster = [point1]
            used_indices.add(i)

            for j, point2 in enumerate(all_points):
                if j in used_indices:
                    continue

                # 计算欧几里得距离
                distance = math.sqrt((point1['x'] - point2['x'])**2 +
                                   (point1['y'] - point2['y'])**2)

                if distance <= cluster_threshold:
                    cluster.append(point2)
                    used_indices.add(j)

            clusters.append(cluster)

        return clusters

    def align_shared_points_precisely(self):
        """
        精确对齐共享角点，确保相邻单元格共享同一个坐标
        """
        shared_points = self.find_shared_points()

        if not shared_points:
            print("  未发现需要对齐的共享角点")
            return

        print(f"  发现 {len(shared_points)} 组共享角点")

        aligned_groups = 0
        total_aligned_points = 0

        for coord_key, point_group in shared_points.items():
            if len(point_group) < 2:
                continue

            # 计算组内所有点的加权中心
            center_coords = self._calculate_group_center(point_group)

            # 检查是否需要对齐
            max_distance = max(
                math.sqrt((p['x'] - center_coords[0])**2 + (p['y'] - center_coords[1])**2)
                for p in point_group
            )

            # 只有当最大距离超过阈值时才进行对齐
            if max_distance > 0.5:  # 0.5像素的精度要求
                aligned_count = 0

                for point in point_group:
                    # 使用保守更新方法
                    if self.conservative_update_point(
                        point['cell_idx'],
                        point['point_name'],
                        center_coords
                    ):
                        aligned_count += 1

                if aligned_count > 0:
                    aligned_groups += 1
                    total_aligned_points += aligned_count

        if total_aligned_points > 0:
            print(f"  精确对齐了 {aligned_groups} 组共享点，共 {total_aligned_points} 个角点")
        else:
            print("  所有共享点已经精确对齐")

    def _calculate_group_center(self, point_group: List[Dict]) -> List[float]:
        """
        计算点组的加权中心

        Args:
            point_group: 点组

        Returns:
            中心坐标
        """
        if not point_group:
            return [0.0, 0.0]

        # 简单平均（可以根据需要添加权重）
        center_x = sum(p['x'] for p in point_group) / len(point_group)
        center_y = sum(p['y'] for p in point_group) / len(point_group)

        return [center_x, center_y]

    def enforce_grid_consistency(self):
        """
        强制网格一致性，确保相邻单元格的边界完全对齐
        """
        print("  强制网格一致性...")

        # 1. 首先对齐所有共享点
        self.align_shared_points_precisely()

        # 2. 然后确保行列边界的一致性
        self._enforce_row_consistency()
        self._enforce_column_consistency()

    def _enforce_row_consistency(self):
        """
        强制行边界一致性
        """
        if not hasattr(self, 'grid_structure') or not self.grid_structure:
            return

        rows = sorted(set(r for r, c in self.grid_structure.keys()))

        for row in rows:
            # 获取该行的所有单元格
            row_cells = []
            for (r, c), cells in self.grid_structure.items():
                if r == row:
                    row_cells.extend(cells)

            if len(row_cells) < 2:
                continue

            # 对齐上边界
            top_coords = []
            bottom_coords = []

            for cell in row_cells:
                bbox = cell['bbox']
                if all(p in bbox for p in ['p1', 'p2', 'p3', 'p4']):
                    top_coords.extend([bbox['p1'][1], bbox['p2'][1]])
                    bottom_coords.extend([bbox['p3'][1], bbox['p4'][1]])

            # 计算平均Y坐标
            if top_coords:
                avg_top_y = sum(top_coords) / len(top_coords)
                self._align_row_boundary(row_cells, 'top', avg_top_y)

            if bottom_coords:
                avg_bottom_y = sum(bottom_coords) / len(bottom_coords)
                self._align_row_boundary(row_cells, 'bottom', avg_bottom_y)

    def _enforce_column_consistency(self):
        """
        强制列边界一致性
        """
        if not hasattr(self, 'grid_structure') or not self.grid_structure:
            return

        cols = sorted(set(c for r, c in self.grid_structure.keys()))

        for col in cols:
            # 获取该列的所有单元格
            col_cells = []
            for (r, c), cells in self.grid_structure.items():
                if c == col:
                    col_cells.extend(cells)

            if len(col_cells) < 2:
                continue

            # 对齐左右边界
            left_coords = []
            right_coords = []

            for cell in col_cells:
                bbox = cell['bbox']
                if all(p in bbox for p in ['p1', 'p2', 'p3', 'p4']):
                    left_coords.extend([bbox['p1'][0], bbox['p4'][0]])
                    right_coords.extend([bbox['p2'][0], bbox['p3'][0]])

            # 计算平均X坐标
            if left_coords:
                avg_left_x = sum(left_coords) / len(left_coords)
                self._align_column_boundary(col_cells, 'left', avg_left_x)

            if right_coords:
                avg_right_x = sum(right_coords) / len(right_coords)
                self._align_column_boundary(col_cells, 'right', avg_right_x)

    def _align_row_boundary(self, row_cells: List[Dict], boundary: str, target_y: float):
        """
        对齐行边界

        Args:
            row_cells: 行内单元格
            boundary: 边界类型（'top' 或 'bottom'）
            target_y: 目标Y坐标
        """
        point_names = ['p1', 'p2'] if boundary == 'top' else ['p3', 'p4']

        for cell in row_cells:
            bbox = cell['bbox']
            cell_idx = self.cells.index(cell)

            for point_name in point_names:
                if point_name in bbox:
                    current_coords = bbox[point_name]
                    new_coords = [current_coords[0], target_y]

                    # 检查调整是否合理
                    if abs(target_y - current_coords[1]) <= self.tolerance:
                        self.conservative_update_point(cell_idx, point_name, new_coords)

    def _align_column_boundary(self, col_cells: List[Dict], boundary: str, target_x: float):
        """
        对齐列边界

        Args:
            col_cells: 列内单元格
            boundary: 边界类型（'left' 或 'right'）
            target_x: 目标X坐标
        """
        point_names = ['p1', 'p4'] if boundary == 'left' else ['p2', 'p3']

        for cell in col_cells:
            bbox = cell['bbox']
            cell_idx = self.cells.index(cell)

            for point_name in point_names:
                if point_name in bbox:
                    current_coords = bbox[point_name]
                    new_coords = [target_x, current_coords[1]]

                    # 检查调整是否合理
                    if abs(target_x - current_coords[0]) <= self.tolerance:
                        self.conservative_update_point(cell_idx, point_name, new_coords)

    def correct_shared_point_angles(self):
        """
        校正共享点的角度一致性，减少小角度分叉
        """
        if not self.angle_correction:
            return

        shared_points = self.find_shared_points()
        corrected_count = 0

        print(f"  发现 {len(shared_points)} 个共享角点")

        for coord_key, point_info in shared_points.items():
            if len(point_info) < 2:
                continue

            # 收集从该点出发的所有线条
            horizontal_lines = []
            vertical_lines = []

            for info in point_info:
                cell = self.cells[info['cell_idx']]
                bbox = cell['bbox']
                point_name = info['point_name']
                coords = info['coords']

                # 找到从该点出发的线条
                connected_points = self._get_connected_points(point_name, bbox)

                for connected_point, direction in connected_points:
                    if connected_point in bbox:
                        target_coords = bbox[connected_point]
                        angle = self.calculate_line_angle(coords, target_coords)

                        line_info = {
                            'cell_idx': info['cell_idx'],
                            'start_point': point_name,
                            'end_point': connected_point,
                            'start_coords': coords,
                            'end_coords': target_coords,
                            'angle': angle,
                            'length': math.sqrt((target_coords[0] - coords[0])**2 +
                                              (target_coords[1] - coords[1])**2)
                        }

                        if direction == 'horizontal':
                            horizontal_lines.append(line_info)
                        else:
                            vertical_lines.append(line_info)

            # 校正水平线角度一致性
            if len(horizontal_lines) > 1:
                corrected_count += self._align_lines_angle(horizontal_lines, 'horizontal')

            # 校正垂直线角度一致性
            if len(vertical_lines) > 1:
                corrected_count += self._align_lines_angle(vertical_lines, 'vertical')

        if corrected_count > 0:
            print(f"  校正了 {corrected_count} 条线的角度分叉")

    def _get_connected_points(self, point_name: str, bbox: Dict) -> List[Tuple[str, str]]:
        """
        获取与指定点连接的其他点

        Args:
            point_name: 点名称
            bbox: 边框信息

        Returns:
            连接点列表，每个元素为(点名称, 方向)
        """
        connections = {
            'p1': [('p2', 'horizontal'), ('p4', 'vertical')],
            'p2': [('p1', 'horizontal'), ('p3', 'vertical')],
            'p3': [('p2', 'vertical'), ('p4', 'horizontal')],
            'p4': [('p3', 'horizontal'), ('p1', 'vertical')]
        }

        return connections.get(point_name, [])

    def _align_lines_angle(self, lines: List[Dict], direction: str) -> int:
        """
        对齐同方向线条的角度

        Args:
            lines: 线条列表
            direction: 方向（'horizontal' 或 'vertical'）

        Returns:
            校正的线条数量
        """
        if len(lines) < 2:
            return 0

        # 计算所有线条的角度
        angles = [line['angle'] for line in lines]

        # 计算目标角度
        if direction == 'horizontal':
            # 水平线应该接近0度或180度
            target_angle = 0.0
            # 如果大部分角度接近180度，使用180度
            if sum(1 for a in angles if abs(a) > 90) > len(angles) / 2:
                target_angle = 180.0
        else:
            # 垂直线应该接近90度或-90度
            target_angle = 90.0
            # 如果大部分角度为负，使用-90度
            if sum(1 for a in angles if a < 0) > len(angles) / 2:
                target_angle = -90.0

        # 校正每条线的终点
        corrected_count = 0
        for line_info in lines:
            current_angle = line_info['angle']
            angle_diff = abs(current_angle - target_angle)

            # 处理角度跨越180度的情况
            if angle_diff > 180:
                angle_diff = 360 - angle_diff

            if angle_diff > self.angle_tolerance:
                # 重新计算终点坐标
                start_coords = line_info['start_coords']
                length = line_info['length']

                target_angle_rad = target_angle * math.pi / 180
                new_end_x = start_coords[0] + length * math.cos(target_angle_rad)
                new_end_y = start_coords[1] + length * math.sin(target_angle_rad)

                # 更新坐标
                cell = self.cells[line_info['cell_idx']]
                cell['bbox'][line_info['end_point']] = [new_end_x, new_end_y]
                corrected_count += 1

        return corrected_count

    def detect_angle_bifurcation(self) -> List[Dict]:
        """
        检测角度分叉问题

        Returns:
            分叉点信息列表
        """
        bifurcation_points = []
        shared_points = self.find_shared_points()

        for coord_key, point_info in shared_points.items():
            if len(point_info) < 2:
                continue

            # 收集从该点出发的所有线条
            outgoing_lines = []

            for info in point_info:
                cell = self.cells[info['cell_idx']]
                bbox = cell['bbox']
                point_name = info['point_name']
                coords = info['coords']

                # 找到从该点出发的线条
                connected_points = self._get_connected_points(point_name, bbox)

                for connected_point, direction in connected_points:
                    if connected_point in bbox:
                        target_coords = bbox[connected_point]
                        angle = self.calculate_line_angle(coords, target_coords)

                        outgoing_lines.append({
                            'cell_idx': info['cell_idx'],
                            'start_point': point_name,
                            'end_point': connected_point,
                            'start_coords': coords,
                            'end_coords': target_coords,
                            'angle': angle,
                            'direction': direction,
                            'length': math.sqrt((target_coords[0] - coords[0])**2 +
                                              (target_coords[1] - coords[1])**2)
                        })

            # 检查是否存在角度分叉
            if len(outgoing_lines) > 1:
                bifurcation_info = self._analyze_bifurcation(coord_key, outgoing_lines)
                if bifurcation_info['has_bifurcation']:
                    bifurcation_points.append(bifurcation_info)

        return bifurcation_points

    def _analyze_bifurcation(self, coord_key: str, lines: List[Dict]) -> Dict:
        """
        分析角度分叉情况

        Args:
            coord_key: 坐标键
            lines: 线条列表

        Returns:
            分叉分析结果
        """
        # 按方向分组
        horizontal_lines = [l for l in lines if l['direction'] == 'horizontal']
        vertical_lines = [l for l in lines if l['direction'] == 'vertical']

        bifurcation_info = {
            'coord_key': coord_key,
            'has_bifurcation': False,
            'horizontal_bifurcation': False,
            'vertical_bifurcation': False,
            'horizontal_lines': horizontal_lines,
            'vertical_lines': vertical_lines,
            'max_angle_diff': 0.0
        }

        # 检查水平线分叉
        if len(horizontal_lines) > 1:
            h_angles = [l['angle'] for l in horizontal_lines]
            h_angle_diff = max(h_angles) - min(h_angles)

            # 处理跨越180度的情况
            if h_angle_diff > 180:
                h_angle_diff = 360 - h_angle_diff

            if h_angle_diff > self.angle_tolerance:
                bifurcation_info['horizontal_bifurcation'] = True
                bifurcation_info['has_bifurcation'] = True
                bifurcation_info['max_angle_diff'] = max(bifurcation_info['max_angle_diff'], h_angle_diff)

        # 检查垂直线分叉
        if len(vertical_lines) > 1:
            v_angles = [l['angle'] for l in vertical_lines]
            v_angle_diff = max(v_angles) - min(v_angles)

            # 处理跨越180度的情况
            if v_angle_diff > 180:
                v_angle_diff = 360 - v_angle_diff

            if v_angle_diff > self.angle_tolerance:
                bifurcation_info['vertical_bifurcation'] = True
                bifurcation_info['has_bifurcation'] = True
                bifurcation_info['max_angle_diff'] = max(bifurcation_info['max_angle_diff'], v_angle_diff)

        return bifurcation_info

    def correct_angle_bifurcation(self, bifurcation_points: List[Dict]) -> int:
        """
        校正角度分叉

        Args:
            bifurcation_points: 分叉点列表

        Returns:
            校正的线条数量
        """
        corrected_count = 0

        for bifurcation in bifurcation_points:
            # 校正水平线分叉
            if bifurcation['horizontal_bifurcation']:
                corrected_count += self._correct_direction_bifurcation(
                    bifurcation['horizontal_lines'], 'horizontal'
                )

            # 校正垂直线分叉
            if bifurcation['vertical_bifurcation']:
                corrected_count += self._correct_direction_bifurcation(
                    bifurcation['vertical_lines'], 'vertical'
                )

        return corrected_count

    def _correct_direction_bifurcation(self, lines: List[Dict], direction: str) -> int:
        """
        校正特定方向的角度分叉

        Args:
            lines: 线条列表
            direction: 方向

        Returns:
            校正的线条数量
        """
        if len(lines) < 2:
            return 0

        # 计算目标角度
        angles = [l['angle'] for l in lines]

        if direction == 'horizontal':
            # 水平线应该接近0度或180度
            # 选择最接近的标准角度
            target_candidates = [0.0, 180.0]
            target_angle = min(target_candidates,
                             key=lambda x: min(abs(a - x) for a in angles))
        else:
            # 垂直线应该接近90度或-90度
            target_candidates = [90.0, -90.0]
            target_angle = min(target_candidates,
                             key=lambda x: min(abs(a - x) for a in angles))

        # 如果启用严格角度校正，使用角度吸附
        if self.strict_angle_correction:
            target_angle = self._snap_to_standard_angle(target_angle, direction)

        # 校正每条线
        corrected_count = 0
        for line in lines:
            current_angle = line['angle']
            angle_diff = abs(current_angle - target_angle)

            # 处理跨越180度的情况
            if angle_diff > 180:
                angle_diff = 360 - angle_diff

            if angle_diff > self.angle_tolerance:
                # 重新计算终点坐标
                start_coords = line['start_coords']
                length = line['length']

                target_angle_rad = target_angle * math.pi / 180
                new_end_x = start_coords[0] + length * math.cos(target_angle_rad)
                new_end_y = start_coords[1] + length * math.sin(target_angle_rad)

                # 更新坐标
                cell = self.cells[line['cell_idx']]
                cell['bbox'][line['end_point']] = [new_end_x, new_end_y]
                corrected_count += 1

        return corrected_count

    def _snap_to_standard_angle(self, angle: float, direction: str) -> float:
        """
        将角度吸附到标准角度

        Args:
            angle: 当前角度
            direction: 方向

        Returns:
            吸附后的角度
        """
        if direction == 'horizontal':
            # 水平线吸附到0度或180度
            if abs(angle) < self.angle_snap_threshold:
                return 0.0
            elif abs(angle - 180) < self.angle_snap_threshold:
                return 180.0
            elif abs(angle + 180) < self.angle_snap_threshold:
                return -180.0
        else:
            # 垂直线吸附到90度或-90度
            if abs(angle - 90) < self.angle_snap_threshold:
                return 90.0
            elif abs(angle + 90) < self.angle_snap_threshold:
                return -90.0

        return angle

    def improved_angle_correction(self):
        """
        执行改进的角度校正
        """
        if not self.angle_correction:
            return

        print("  执行改进的角度校正...")

        # 1. 检测角度分叉
        bifurcation_points = self.detect_angle_bifurcation()

        if not bifurcation_points:
            print("    未发现角度分叉问题")
            return

        print(f"    发现 {len(bifurcation_points)} 个角度分叉点")

        # 2. 校正角度分叉
        corrected_count = self.correct_angle_bifurcation(bifurcation_points)

        print(f"    校正了 {corrected_count} 条线的角度分叉")

        # 3. 如果启用严格模式，进行全局角度标准化
        if self.strict_angle_correction:
            global_corrected = self._global_angle_standardization()
            print(f"    全局角度标准化校正了 {global_corrected} 条线")

    def _global_angle_standardization(self) -> int:
        """
        全局角度标准化

        Returns:
            校正的线条数量
        """
        corrected_count = 0

        for cell in self.cells:
            bbox = cell['bbox']
            if not all(p in bbox for p in ['p1', 'p2', 'p3', 'p4']):
                continue

            # 处理四条边
            edges = [
                ('p1', 'p2', 'horizontal'),  # 上边
                ('p2', 'p3', 'vertical'),    # 右边
                ('p3', 'p4', 'horizontal'),  # 下边
                ('p4', 'p1', 'vertical')     # 左边
            ]

            for start_point, end_point, direction in edges:
                start_coords = bbox[start_point]
                end_coords = bbox[end_point]

                current_angle = self.calculate_line_angle(start_coords, end_coords)
                target_angle = self._snap_to_standard_angle(current_angle, direction)

                angle_diff = abs(current_angle - target_angle)
                if angle_diff > 180:
                    angle_diff = 360 - angle_diff

                if angle_diff > self.angle_tolerance:
                    # 重新计算终点
                    length = math.sqrt((end_coords[0] - start_coords[0])**2 +
                                     (end_coords[1] - start_coords[1])**2)

                    target_angle_rad = target_angle * math.pi / 180
                    new_end_x = start_coords[0] + length * math.cos(target_angle_rad)
                    new_end_y = start_coords[1] + length * math.sin(target_angle_rad)

                    bbox[end_point] = [new_end_x, new_end_y]
                    corrected_count += 1

        return corrected_count

    def _evaluate_optimization_quality(self):
        """
        评估优化质量
        """
        try:
            # 计算边界对齐质量
            horizontal_errors = self._calculate_boundary_errors('horizontal')
            vertical_errors = self._calculate_boundary_errors('vertical')

            if horizontal_errors:
                h_avg = sum(horizontal_errors) / len(horizontal_errors)
                h_max = max(horizontal_errors)
                print(f"  水平对齐质量: 平均误差 {h_avg:.2f}px, 最大误差 {h_max:.2f}px")

            if vertical_errors:
                v_avg = sum(vertical_errors) / len(vertical_errors)
                v_max = max(vertical_errors)
                print(f"  垂直对齐质量: 平均误差 {v_avg:.2f}px, 最大误差 {v_max:.2f}px")

        except Exception as e:
            print(f"  质量评估失败: {e}")

    def _calculate_boundary_errors(self, direction: str) -> List[float]:
        """
        计算边界对齐误差

        Args:
            direction: 'horizontal' 或 'vertical'

        Returns:
            误差列表
        """
        errors = []

        try:
            if direction == 'horizontal':
                # 检查水平边界的对齐误差
                for row in set(r for r, c in self.grid_structure.keys()):
                    row_cells = [cell for (r, c), cells in self.grid_structure.items()
                               if r == row for cell in cells]
                    if len(row_cells) > 1:
                        # 检查同一行单元格的上下边界对齐
                        top_coords = [cell['bbox']['p1'][1] for cell in row_cells] + \
                                   [cell['bbox']['p2'][1] for cell in row_cells]
                        bottom_coords = [cell['bbox']['p3'][1] for cell in row_cells] + \
                                      [cell['bbox']['p4'][1] for cell in row_cells]

                        if len(top_coords) > 1:
                            errors.append(max(top_coords) - min(top_coords))
                        if len(bottom_coords) > 1:
                            errors.append(max(bottom_coords) - min(bottom_coords))

            elif direction == 'vertical':
                # 检查垂直边界的对齐误差
                for col in set(c for r, c in self.grid_structure.keys()):
                    col_cells = [cell for (r, c), cells in self.grid_structure.items()
                               if c == col for cell in cells]
                    if len(col_cells) > 1:
                        # 检查同一列单元格的左右边界对齐
                        left_coords = [cell['bbox']['p1'][0] for cell in col_cells] + \
                                    [cell['bbox']['p4'][0] for cell in col_cells]
                        right_coords = [cell['bbox']['p2'][0] for cell in col_cells] + \
                                     [cell['bbox']['p3'][0] for cell in col_cells]

                        if len(left_coords) > 1:
                            errors.append(max(left_coords) - min(left_coords))
                        if len(right_coords) > 1:
                            errors.append(max(right_coords) - min(right_coords))

        except Exception:
            pass  # 忽略计算错误，返回空列表

        return errors


    def optimize_alignment(self):
        """执行透视感知的角点对齐优化"""
        print("构建网格结构...")
        self.build_grid_structure()

        print("对齐行边界...")
        self.align_row_boundaries()

        print("对齐列边界...")
        self.align_column_boundaries()

        print("微调相近角点...")
        self.fine_tune_nearby_points()

        # 角度校正步骤
        if self.angle_correction:
            print("校正角度一致性...")
            self.correct_shared_point_angles()

        print("透视感知优化完成！")

        # 评估优化质量
        self._evaluate_optimization_quality()

    def save_optimized_annotation(self, output_path: str):
        """
        保存优化后的标注文件，保持原有属性不变

        Args:
            output_path: 输出文件路径
        """
        # 确保输出目录存在
        output_dir = os.path.dirname(output_path)
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)

        # 基于原始数据创建输出数据，保护所有原始属性
        output_data = self.original_data.copy()

        # 更新优化后的单元格数据
        output_data['cells'] = self.cells

        # 确保基本属性正确
        output_data['table_ind'] = self.table_ind
        output_data['image_path'] = self.image_path

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)

        print(f"优化后的标注文件已保存到: {output_path}")

    def optimize_table_annotation(self, annotation_file: str, output_file: Optional[str] = None,
                                image_file: Optional[str] = None) -> Dict[str, Any]:
        """
        优化表格标注文件的主入口方法

        Args:
            annotation_file: 输入的标注文件路径
            output_file: 输出文件路径，如果为None则覆盖原文件
            image_file: 对应的图片文件路径，用于自适应阈值计算

        Returns:
            包含优化结果的字典
        """
        try:
            # 加载标注文件
            self.load_annotation(annotation_file, image_file)

            # 执行优化
            self.optimize_alignment()

            # 保存结果
            if output_file is None:
                output_file = annotation_file

            self.save_optimized_annotation(output_file)

            return {
                'success': True,
                'input_file': annotation_file,
                'output_file': output_file,
                'cell_count': len(self.cells),
                'adaptive_threshold': self.tolerance if self.adaptive_threshold else None,
                'image_info': self.image_info
            }

        except Exception as e:
            return {
                'success': False,
                'input_file': annotation_file,
                'output_file': output_file or annotation_file,
                'error': str(e)
            }


class BatchProcessor:
    """批量处理器类"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化批量处理器

        Args:
            config: 配置字典
        """
        self.config = config

    def find_annotation_files(self, input_dir: str) -> List[Path]:
        """查找所有标注文件"""
        input_path = Path(input_dir)
        pattern = self.config.get('annotation_pattern', '*_table_annotation.json')
        return list(input_path.glob(pattern))

    def find_corresponding_image(self, annotation_file: Path) -> Optional[str]:
        """查找对应的图片文件"""
        base_name = annotation_file.stem.replace('_table_annotation', '')
        image_dir = annotation_file.parent

        image_extensions = self.config.get('image_extensions', ['.jpg', '.jpeg', '.png', '.bmp'])

        for ext in image_extensions:
            image_path = image_dir / f"{base_name}{ext}"
            if image_path.exists():
                return str(image_path)

        return None

    def copy_image_file(self, image_file: str, output_dir: str) -> bool:
        """
        复制图片文件到输出目录

        Args:
            image_file: 原图片文件路径
            output_dir: 输出目录路径

        Returns:
            复制是否成功
        """
        try:
            import shutil

            image_path = Path(image_file)
            output_image_path = Path(output_dir) / image_path.name

            # 确保输出目录存在
            output_image_path.parent.mkdir(parents=True, exist_ok=True)

            # 复制图片文件
            shutil.copy2(image_file, output_image_path)
            return True

        except Exception as e:
            print(f"复制图片文件失败 {image_file}: {e}")
            return False

    def process_batch(self) -> Dict[str, Any]:
        """执行批量处理"""
        input_dir = self.config['input_dir']
        output_dir = self.config['output_dir']
        max_workers = self.config.get('max_workers', 4)

        # 查找所有标注文件
        annotation_files = self.find_annotation_files(input_dir)

        if not annotation_files:
            return {
                'success': False,
                'error': f'在 {input_dir} 中未找到标注文件'
            }

        print(f"找到 {len(annotation_files)} 个标注文件")
        print(f"输出目录: {output_dir}")
        print(f"并行处理数: {max_workers}")
        print("=" * 60)

        # 统计信息
        stats = {
            'total_files': len(annotation_files),
            'successful': 0,
            'failed': 0,
            'images_copied': 0,
            'images_failed': 0,
            'total_time': 0,
            'results': []
        }

        start_time = time.time()

        # 并行处理
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交任务
            future_to_file = {}
            for annotation_file in annotation_files:
                # 确定输出文件路径
                relative_path = annotation_file.relative_to(input_dir)
                output_file = Path(output_dir) / relative_path

                # 查找对应图片
                image_file = self.find_corresponding_image(annotation_file)

                # 创建优化器实例
                optimizer = PerspectiveAwareOptimizer(
                    tolerance=self.config.get('tolerance', 1.5),
                    preserve_perspective=self.config.get('preserve_perspective', True),
                    adaptive_threshold=self.config.get('adaptive_threshold', False),
                    quality_aware=self.config.get('quality_aware', False),
                    angle_correction=self.config.get('angle_correction', False),
                    conservative_mode=self.config.get('conservative_mode', True)
                )

                future = executor.submit(
                    optimizer.optimize_table_annotation,
                    str(annotation_file),
                    str(output_file),
                    image_file
                )
                future_to_file[future] = annotation_file

            # 处理结果
            for i, future in enumerate(as_completed(future_to_file), 1):
                annotation_file = future_to_file[future]
                result = future.result()

                stats['results'].append(result)

                if result['success']:
                    stats['successful'] += 1
                    print(f"[{i:3d}/{len(annotation_files)}] ✅ {annotation_file.name}")
                    if 'adaptive_threshold' in result and result['adaptive_threshold']:
                        print(f"    自适应阈值: {result['adaptive_threshold']:.2f}")

                    # 复制对应的图片文件（如果启用）
                    if self.config.get('copy_images', True):
                        image_file = self.find_corresponding_image(annotation_file)
                        if image_file:
                            if self.copy_image_file(image_file, output_dir):
                                stats['images_copied'] += 1
                                print(f"    📷 图片已复制: {Path(image_file).name}")
                            else:
                                stats['images_failed'] += 1
                                print(f"    ❌ 图片复制失败: {Path(image_file).name}")
                        else:
                            stats['images_failed'] += 1
                            print(f"    ⚠️  未找到对应图片")
                else:
                    stats['failed'] += 1
                    print(f"[{i:3d}/{len(annotation_files)}] ❌ {annotation_file.name} - {result['error']}")

        stats['total_time'] = time.time() - start_time

        # 输出统计
        print("\n" + "=" * 60)
        print("批量处理完成")
        print("=" * 60)
        print(f"📄 标注文件:")
        print(f"   总文件数: {stats['total_files']}")
        print(f"   成功处理: {stats['successful']}")
        print(f"   处理失败: {stats['failed']}")
        print(f"   成功率: {stats['successful']/stats['total_files']*100:.1f}%")
        print(f"📷 图片文件:")
        print(f"   成功复制: {stats['images_copied']}")
        print(f"   复制失败: {stats['images_failed']}")
        if stats['images_copied'] + stats['images_failed'] > 0:
            print(f"   复制成功率: {stats['images_copied']/(stats['images_copied']+stats['images_failed'])*100:.1f}%")
        print(f"⏱️  总处理时间: {stats['total_time']:.1f}秒")

        return stats


# ==================== 默认配置区域 ====================
# 注意：主配置在文件顶部的 CONFIG 字典中，这里只是作为命令行参数的默认值
# ================================================


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='表格标注优化器 - 使用透视感知算法优化表格角点对齐',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python table_annotation_optimizer.py
  python table_annotation_optimizer.py --input-dir my_data --output-dir my_output
  python table_annotation_optimizer.py --tolerance 5.0 --workers 8
  python table_annotation_optimizer.py --no-adaptive --no-perspective
        """
    )

    parser.add_argument('--input-dir', help='输入目录路径（默认: borderless_table）')
    parser.add_argument('--output-dir', help='输出目录路径（默认: borderless_table_optimized）')
    parser.add_argument('--tolerance', type=float, help='基础容差阈值，单位像素（默认: 3.0）')
    parser.add_argument('--workers', type=int, help='并行处理线程数（默认: 4）')
    parser.add_argument('--no-perspective', action='store_true', help='不保持透视变换，强制矩形对齐')
    parser.add_argument('--no-adaptive', action='store_true', help='不使用自适应阈值，使用固定阈值')
    parser.add_argument('--no-copy-images', action='store_true', help='不复制图片文件到输出目录')
    parser.add_argument('--pattern', help='标注文件匹配模式（默认: *_table_annotation.json）')

    args = parser.parse_args()

    # 更新配置（使用顶部的 CONFIG 作为基础配置）
    config = CONFIG.copy()
    if args.input_dir:
        config['input_dir'] = args.input_dir
    if args.output_dir:
        config['output_dir'] = args.output_dir
    if args.tolerance:
        config['tolerance'] = args.tolerance
    if args.workers:
        config['max_workers'] = args.workers
    if args.no_perspective:
        config['preserve_perspective'] = False
    if args.no_adaptive:
        config['adaptive_threshold'] = False
    if args.no_copy_images:
        config['copy_images'] = False
    if args.pattern:
        config['annotation_pattern'] = args.pattern

    # 检查输入目录
    if not os.path.exists(config['input_dir']):
        print(f"❌ 错误: 输入目录 {config['input_dir']} 不存在")
        return 1

    print("=" * 60)
    print("🔧 表格标注优化器 v3.0")
    print("=" * 60)
    print(f"📁 输入目录: {config['input_dir']}")
    print(f"📁 输出目录: {config['output_dir']}")
    print(f"🎯 基础阈值: {config['tolerance']} 像素")
    print(f"🔄 保持透视: {'是' if config['preserve_perspective'] else '否'}")
    print(f"📊 自适应阈值: {'是' if config['adaptive_threshold'] else '否'}")
    print(f"📷 复制图片: {'是' if config.get('copy_images', True) else '否'}")
    print(f"⚡ 并行线程: {config['max_workers']}")
    print(f"🔍 文件模式: {config['annotation_pattern']}")
    print()

    # 执行批量处理
    processor = BatchProcessor(config)
    stats = processor.process_batch()

    if stats.get('success', True) and stats['failed'] == 0:
        print(f"\n✅ 处理完成！优化后的文件保存在: {config['output_dir']}")
        print(f"📊 处理统计: {stats['successful']}/{stats['total_files']} 成功")
        return 0
    else:
        print(f"\n❌ 处理过程中出现错误")
        if 'error' in stats:
            print(f"错误信息: {stats['error']}")
        return 1


if __name__ == "__main__":
    exit(main())
